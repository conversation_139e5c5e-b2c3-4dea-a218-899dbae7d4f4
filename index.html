<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-g" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Social Post Generator</title>
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link
      href="https://fonts.googleapis.com/css2?family=Poppins:wght@400;600;700&display=swap"
      rel="stylesheet"
    />
    <style>
      :root {
        --brand-color: #4f46e5;
        --brand-color-light: #6366f1;
        --background-color: #f8fafc;
        --text-color: #1e293b;
        --card-background: #ffffff;
        --border-color: #e2e8f0;
        --shadow-color: rgba(0, 0, 0, 0.1);
      }
      body {
        font-family: 'Poppins', sans-serif;
        background-color: var(--background-color);
        color: var(--text-color);
        margin: 0;
        padding: 0;
        min-height: 100vh;
        box-sizing: border-box;
      }
      #root {
        width: 100%;
        height: 100%;
      }
    </style>
  <script type="importmap">
{
  "imports": {
    "react-dom/": "https://aistudiocdn.com/react-dom@^19.1.1/",
    "react/": "https://aistudiocdn.com/react@^19.1.1/",
    "react": "https://aistudiocdn.com/react@^19.1.1",
    "@google/genai": "https://aistudiocdn.com/@google/genai@^1.16.0"
  }
}
</script>
<link rel="stylesheet" href="/index.css">
</head>
  <body>
    <div id="root"></div>
    <script type="module">
import React, { useState, useRef, useEffect, useCallback } from 'react';
import ReactDOM from 'react-dom/client';
import { GoogleGenAI, Type } from '@google/genai';

const styles = {
      appContainer: {
        display: 'flex',
        flexDirection: 'column',
        minHeight: '100vh',
        backgroundColor: 'var(--background-color)',
        padding: '2rem',
        boxSizing: 'border-box',
      },
      appLayout: {
        display: 'grid',
        gridTemplateColumns: '1.5fr 1fr',
        gap: '2rem',
        alignItems: 'flex-start',
        flex: 1,
        width: '100%',
        maxWidth: '1280px',
        margin: '0 auto',
      },
      previewPanel: {
        position: 'sticky',
        top: '2rem',
        height: 'calc(100vh - 4rem)',
        backgroundColor: '#f1f5f9',
        borderRadius: '16px',
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        padding: '2rem',
        boxSizing: 'border-box',
      },
      controlsSidebar: {
        backgroundColor: 'var(--card-background)',
        padding: '2rem',
        borderRadius: '16px',
        boxShadow: '0 10px 25px -5px var(--shadow-color)',
        border: '1px solid var(--border-color)',
        display: 'flex',
        flexDirection: 'column',
        gap: '1.5rem',
      },
      header: {
        borderBottom: '1px solid var(--border-color)',
        paddingBottom: '1.5rem',
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        flexWrap: 'wrap',
        gap: '1rem',
      },
      headerContent: {
          textAlign: 'left',
      },
      title: {
        fontSize: '1.75rem',
        fontWeight: '700',
        color: 'var(--brand-color)',
        margin: '0',
      },
      subtitle: {
        fontSize: '0.9rem',
        color: '#475569',
        marginTop: '0.25rem',
        textAlign: 'left',
      },
      form: {
        display: 'flex',
        flexDirection: 'column',
        gap: '1rem',
      },
      input: {
        flexGrow: 1,
        padding: '0.75rem 1rem',
        fontSize: '1rem',
        borderRadius: '8px',
        border: '1px solid var(--border-color)',
        outline: 'none',
        transition: 'border-color 0.2s, box-shadow 0.2s',
        boxSizing: 'border-box',
        width: '100%',
        backgroundColor: '#fff',
        color: '#1e293b'
      },
      textarea: {
        flexGrow: 1,
        padding: '0.75rem 1rem',
        fontSize: '1rem',
        borderRadius: '8px',
        border: '1px solid var(--border-color)',
        outline: 'none',
        transition: 'border-color 0.2s, box-shadow 0.2s',
        boxSizing: 'border-box',
        width: '100%',
        fontFamily: 'Poppins, sans-serif',
        resize: 'vertical',
        backgroundColor: '#fff',
        color: '#1e293b'
      },
      button: {
        padding: '0.75rem 1.5rem',
        fontSize: '1rem',
        fontWeight: '600',
        color: 'white',
        backgroundColor: 'var(--brand-color)',
        border: 'none',
        borderRadius: '8px',
        cursor: 'pointer',
        transition: 'background-color 0.2s, transform 0.1s',
        width: '100%',
      },
      buttonDisabled: {
        backgroundColor: '#d1d5db',
        cursor: 'not-allowed',
      },
      secondaryButton: {
        padding: '0.75rem 1.5rem',
        fontSize: '1rem',
        fontWeight: '600',
        color: '#475569',
        backgroundColor: '#f1f5f9',
        border: '1px solid var(--border-color)',
        borderRadius: '8px',
        cursor: 'pointer',
        transition: 'background-color 0.2s',
      },
      accordionSection: {
        border: '1px solid var(--border-color)',
        borderRadius: '12px',
        backgroundColor: '#fdfdff',
      },
      accordionHeader: {
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        padding: '1rem 1.25rem',
        cursor: 'pointer',
        borderBottom: '1px solid transparent',
        transition: 'border-color 0.2s',
      },
      accordionHeaderOpen: {
        borderBottomColor: 'var(--border-color)',
      },
      accordionTitle: {
        fontSize: '1rem',
        fontWeight: '600',
        margin: 0,
        color: 'var(--brand-color)',
      },
      accordionIcon: {
        transition: 'transform 0.3s ease',
        color: '#64748b',
        width: '20px',
        height: '20px',
      },
      accordionIconOpen: {
        transform: 'rotate(180deg)',
      },
      accordionContent: {
        padding: '1.25rem',
        display: 'flex',
        flexDirection: 'column',
        gap: '1.25rem',
      },
      optionsContainer: {
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        flexWrap: 'wrap',
        gap: '1.5rem',
      },
      optionItem: {
        display: 'flex',
        alignItems: 'center',
        gap: '0.75rem',
      },
      colorLabel: {
        fontWeight: '600',
        color: '#475569',
      },
      colorInput: {
        width: '40px',
        height: '40px',
        border: 'none',
        borderRadius: '6px',
        cursor: 'pointer',
        backgroundColor: 'transparent',
      },
      switchTrack: {
        width: '50px',
        height: '28px',
        borderRadius: '14px',
        padding: '4px',
        cursor: 'pointer',
        border: 'none',
        display: 'flex',
        alignItems: 'center',
        transition: 'background-color 0.2s ease',
      },
      switchKnob: {
        width: '20px',
        height: '20px',
        borderRadius: '50%',
        backgroundColor: 'white',
        display: 'block',
        transition: 'transform 0.2s ease',
      },
      uploadLabel: {
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        justifyContent: 'center',
        padding: '1.5rem',
        border: '2px dashed var(--border-color)',
        borderRadius: '8px',
        cursor: 'pointer',
        backgroundColor: '#f9fafb',
        color: '#475569',
        textAlign: 'center',
        transition: 'border-color 0.2s, background-color 0.2s',
      },
      previewContainer: {
        display: 'flex',
        alignItems: 'center',
        gap: '1rem',
        padding: '1rem',
        border: '1px solid var(--border-color)',
        borderRadius: '8px',
        marginTop: '1rem',
        backgroundColor: '#f9fafb',
      },
      previewImage: {
        height: '60px',
        borderRadius: '4px',
        border: '1px solid var(--border-color)',
      },
      clearButton: {
        padding: '0.5rem 1rem',
        fontSize: '0.9rem',
        fontWeight: '600',
        color: '#ef4444',
        backgroundColor: '#fee2e2',
        border: 'none',
        borderRadius: '6px',
        cursor: 'pointer',
      },
      placeholder: {
        textAlign: 'center',
        color: '#64748b',
        maxWidth: '300px',
      },
      placeholderTitle: {
        fontSize: '1.5rem',
        fontWeight: '600',
        color: '#475569',
      },
      loaderContainer: {
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        gap: '1rem',
        padding: '2rem',
        borderRadius: '8px',
      },
      spinner: {
        width: '40px',
        height: '40px',
        border: '4px solid rgba(0,0,0,0.1)',
        borderTopColor: 'var(--brand-color)',
        borderRadius: '50%',
        animation: 'spin 1s linear infinite',
      },
      loadingText: {
        color: '#475569',
        fontWeight: '600',
      },
      error: {
        color: '#dc2626',
        backgroundColor: '#fee2e2',
        padding: '1rem',
        borderRadius: '8px',
        width: '100%',
        boxSizing: 'border-box',
      },
      postImage: {
        maxWidth: '100%',
        maxHeight: '100%',
        objectFit: 'contain',
        borderRadius: '8px',
        boxShadow: '0 4px 15px -2px var(--shadow-color)',
      },
      postActions: {
          display: 'grid',
          gridTemplateColumns: '1fr 1fr',
          gap: '1rem',
          width: '100%',
      },
      downloadButton: {
        width: '100%',
        padding: '0.75rem 1.5rem',
        fontSize: '1rem',
        fontWeight: '600',
        color: 'var(--brand-color)',
        backgroundColor: 'white',
        border: '2px solid var(--brand-color)',
        borderRadius: '8px',
        cursor: 'pointer',
        transition: 'background-color 0.2s, color 0.2s',
      },
      shareButton: {
        width: '100%',
      },
      editSection: {
        width: '100%',
        boxSizing: 'border-box',
        textAlign: 'left'
      },
      editTitle: {
        margin: '0 0 1.5rem 0',
        fontSize: '1.2rem',
        fontWeight: '600',
        color: '#1e293b',
      },
      editForm: {
        display: 'grid',
        gridTemplateColumns: '1fr',
        gap: '1.25rem',
      },
      editField: {
        display: 'flex',
        flexDirection: 'column',
        gap: '0.5rem',
      },
      editLabel: {
        fontWeight: '600',
        color: '#334155',
        fontSize: '0.9rem',
      },
      editInputProminent: {
        fontSize: '1.25rem',
        fontWeight: '600',
      },
      editTextAreaProminent: {
        fontSize: '1rem',
        minHeight: '100px',
      },
      tagsContainer: {
          display: 'flex',
          flexWrap: 'wrap',
          gap: '0.5rem',
          padding: '0.5rem',
          backgroundColor: 'white',
          border: '1px solid var(--border-color)',
          borderRadius: '8px',
      },
      tagPill: {
          display: 'flex',
          alignItems: 'center',
          gap: '0.5rem',
          backgroundColor: '#eef2ff',
          color: 'var(--brand-color)',
          padding: '0.25rem 0.75rem',
          borderRadius: '16px',
          fontWeight: '600',
          fontSize: '0.9rem',
      },
      tagRemoveBtn: {
          background: 'none',
          border: 'none',
          color: 'var(--brand-color)',
          cursor: 'pointer',
          padding: '0',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          opacity: '0.7',
          transition: 'opacity 0.2s',
      },
      featureItem: {
          display: 'flex',
          gap: '0.75rem',
          alignItems: 'center',
      },
      featureRemoveBtn: {
          padding: '0.5rem',
          fontSize: '0.9rem',
          fontWeight: '600',
          color: '#ef4444',
          backgroundColor: '#fee2e2',
          border: 'none',
          borderRadius: '6px',
          cursor: 'pointer',
          lineHeight: '1',
      },
      bulletIconGrid: {
          display: 'grid',
          gridTemplateColumns: 'repeat(5, 1fr)',
          gap: '0.5rem',
          marginBottom: '1rem',
      },
      bulletIconOption: {
          width: '40px',
          height: '40px',
          border: '2px solid #e2e8f0',
          borderRadius: '8px',
          cursor: 'pointer',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          backgroundColor: 'white',
          transition: 'all 0.2s',
      },
      bulletIconOptionSelected: {
          borderColor: 'var(--brand-color)',
          backgroundColor: '#eef2ff',
      },
      bulletIconPreview: {
          width: '20px',
          height: '20px',
      },
      templateGrid: {
          display: 'grid',
          gridTemplateColumns: 'repeat(2, 1fr)',
          gap: '0.75rem',
          marginBottom: '1rem',
      },
      templateOption: {
          padding: '0.75rem',
          border: '2px solid #e2e8f0',
          borderRadius: '8px',
          cursor: 'pointer',
          backgroundColor: 'white',
          transition: 'all 0.2s',
          textAlign: 'center',
      },
      templateOptionSelected: {
          borderColor: 'var(--brand-color)',
          backgroundColor: '#eef2ff',
      },
      templatePreview: {
          width: '60px',
          height: '60px',
          border: '2px solid #cbd5e1',
          borderRadius: '4px',
          margin: '0 auto 0.5rem',
          backgroundColor: '#f8fafc',
          position: 'relative',
          overflow: 'hidden',
      },
      templatePreviewSelected: {
          borderColor: 'var(--brand-color)',
          backgroundColor: '#eef2ff',
      },
      templateName: {
          fontSize: '0.85rem',
          fontWeight: '600',
          color: '#1e293b',
          marginBottom: '0.25rem',
      },
      templateDescription: {
          fontSize: '0.75rem',
          color: '#64748b',
          lineHeight: '1.2',
      },
      addFeatureBtn: {
        justifySelf: 'start',
        padding: '0.5rem 1rem',
        fontSize: '0.9rem',
        fontWeight: '600',
        color: '#166534',
        backgroundColor: '#dcfce7',
        border: 'none',
        borderRadius: '6px',
        cursor: 'pointer',
      },
      updateImageBtn: {
          marginTop: '1rem',
          width: '100%',
          padding: '0.875rem',
          fontSize: '1rem',
          fontWeight: '600',
      },
      captionResult: {
        textAlign: 'left',
        width: '100%',
        boxSizing: 'border-box'
      },
      captionTextarea: {
        width: '100%',
        padding: '0.75rem 1rem',
        fontSize: '1rem',
        fontFamily: 'Poppins, sans-serif',
        borderRadius: '8px',
        border: '1px solid var(--border-color)',
        backgroundColor: 'white',
        resize: 'vertical',
        boxSizing: 'border-box',
        color: '#1e293b',
        lineHeight: '1.6',
        minHeight: '150px',
      },
      copyButton: {
        alignSelf: 'flex-end',
        padding: '0.5rem 1.25rem',
        fontSize: '0.9rem',
        fontWeight: '600',
        color: 'var(--brand-color)',
        backgroundColor: '#eef2ff',
        border: '1px solid #c7d2fe',
        borderRadius: '8px',
        cursor: 'pointer',
        transition: 'all 0.2s',
        marginTop: '0.75rem',
      },
      modalOverlay: {
        position: 'fixed',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        backgroundColor: 'rgba(0, 0, 0, 0.7)',
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        zIndex: 1000,
      },
      modalContent: {
        backgroundColor: 'white',
        padding: '2rem',
        borderRadius: '16px',
        boxShadow: '0 10px 30px rgba(0,0,0,0.2)',
        textAlign: 'center',
        maxHeight: '95vh',
        maxWidth: '90vw',
        boxSizing: 'border-box',
        display: 'flex',
        flexDirection: 'column',
      },
      modalActions: {
          marginTop: '1.5rem',
          display: 'flex',
          justifyContent: 'center',
          gap: '1rem',
      },
      shareModalOverlay: {
        position: 'fixed',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        backgroundColor: 'rgba(0, 0, 0, 0.7)',
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        zIndex: 1001,
      },
      shareModalContent: {
        backgroundColor: 'white',
        padding: '2rem 2.5rem',
        borderRadius: '16px',
        boxShadow: '0 10px 30px rgba(0,0,0,0.2)',
        textAlign: 'left',
        maxWidth: '500px',
        width: '90%',
      },
      shareModalTitle: {
        margin: '0 0 1rem 0',
        fontSize: '1.5rem',
        fontWeight: '700',
        color: 'var(--brand-color)',
      },
      shareModalInstructions: {
        paddingLeft: '1.5rem',
        margin: '0 0 2rem 0',
        color: '#334155',
        lineHeight: '1.6',
        listStyle: 'decimal',
      },
      shareModalActions: {
          display: 'flex',
          justifyContent: 'flex-end',
          gap: '1rem',
      },
      apiKeyContainer: {
        textAlign: 'center',
        padding: '4rem 2rem',
        backgroundColor: 'var(--card-background)',
        borderRadius: '16px',
        width: '100%',
        maxWidth: '600px',
        margin: 'auto',
        border: '1px solid var(--border-color)',
      },
      apiKeyTitle: {
        fontSize: '1.75rem',
        color: 'var(--brand-color)',
        marginBottom: '0.5rem',
      },
      apiKeySubtitle: {
        fontSize: '1rem',
        color: '#475569',
        maxWidth: '500px',
        margin: '0 auto 1.5rem auto',
        lineHeight: '1.6',
      },
      apiKeyForm: {
          display: 'flex',
          gap: '1rem',
          maxWidth: '500px',
          margin: '0 auto',
      },
      changeKeyButton: {
        padding: '0.5rem 1rem',
        fontSize: '0.9rem',
        fontWeight: '600',
        color: '#475569',
        backgroundColor: '#f1f5f9',
        border: '1px solid var(--border-color)',
        borderRadius: '8px',
        cursor: 'pointer',
        transition: 'background-color 0.2s',
      },
      footer: {
        textAlign: 'center',
        padding: '2rem 1rem 1rem 1rem',
        fontSize: '0.9rem',
        color: '#64748b',
        width: '100%',
        maxWidth: '1280px',
        margin: '0 auto',
        boxSizing: 'border-box',
      },
      footerLink: {
        color: 'var(--brand-color)',
        textDecoration: 'none',
        fontWeight: '600',
      },
      cookieBanner: {
        position: 'fixed',
        bottom: '1rem',
        left: '50%',
        transform: 'translateX(-50%)',
        backgroundColor: '#1e293b',
        color: 'white',
        padding: '1rem 1.5rem',
        borderRadius: '12px',
        boxShadow: '0 5px 20px rgba(0,0,0,0.2)',
        display: 'flex',
        alignItems: 'center',
        gap: '1.5rem',
        zIndex: 2000,
        maxWidth: 'calc(100% - 2rem)',
        boxSizing: 'border-box',
      },
      cookieText: {
        margin: 0,
        fontSize: '0.9rem',
      },
      cookieButton: {
        padding: '0.6rem 1.2rem',
        fontSize: '0.9rem',
        fontWeight: '600',
        color: 'var(--brand-color)',
        backgroundColor: 'white',
        border: 'none',
        borderRadius: '8px',
        cursor: 'pointer',
        whiteSpace: 'nowrap',
      },
};

const ChevronIcon = ({ isOpen }) => React.createElement('svg', {
    style: { ...styles.accordionIcon, ...(isOpen && styles.accordionIconOpen) },
    'aria-hidden': true,
    viewBox: '0 0 24 24',
    fill: 'none',
    stroke: 'currentColor',
    strokeWidth: '2.5',
    strokeLinecap: 'round',
    strokeLinejoin: 'round',
}, React.createElement('path', { d: 'm6 9 6 6 6-6' }));

const RemoveIcon = () => React.createElement('svg', {
    width: "14",
    height: "14",
    viewBox: "0 0 14 14",
    fill: "none",
    stroke: "currentColor",
    strokeWidth: "2",
    strokeLinecap: "round",
    strokeLinejoin: "round"
},
  React.createElement('path', { d: "M13 1 1 13" }),
  React.createElement('path', { d: "m1 1 12 12" })
);

// Bullet Icon SVG Components
const BulletIconSVG = ({ iconType, color = '#334155', size = 20 }) => {
  const iconSVGs = {
    checkmark: React.createElement('svg', { width: size, height: size, viewBox: '0 0 20 20', fill: 'none' },
      React.createElement('path', { d: 'M4 10l4 4 8-8', stroke: color, strokeWidth: '2.5', strokeLinecap: 'round', strokeLinejoin: 'round' })
    ),
    circle: React.createElement('svg', { width: size, height: size, viewBox: '0 0 20 20', fill: 'none' },
      React.createElement('circle', { cx: '10', cy: '10', r: '6', fill: color })
    ),
    square: React.createElement('svg', { width: size, height: size, viewBox: '0 0 20 20', fill: 'none' },
      React.createElement('rect', { x: '4', y: '4', width: '12', height: '12', fill: color })
    ),
    diamond: React.createElement('svg', { width: size, height: size, viewBox: '0 0 20 20', fill: 'none' },
      React.createElement('path', { d: 'M10 2 18 10 10 18 2 10z', fill: color })
    ),
    star: React.createElement('svg', { width: size, height: size, viewBox: '0 0 20 20', fill: 'none' },
      React.createElement('path', { d: 'M10 2l2.5 5.5L18 8.5l-4 4 1 6-5-2.5L5 18.5l1-6-4-4 5.5-1L10 2z', fill: color })
    ),
    arrow: React.createElement('svg', { width: size, height: size, viewBox: '0 0 20 20', fill: 'none' },
      React.createElement('path', { d: 'M2 8h12l-4-4m4 4l-4 4', stroke: color, strokeWidth: '2', strokeLinecap: 'round', strokeLinejoin: 'round' })
    ),
    plus: React.createElement('svg', { width: size, height: size, viewBox: '0 0 20 20', fill: 'none' },
      React.createElement('path', { d: 'M10 4v12m-6-6h12', stroke: color, strokeWidth: '2.5', strokeLinecap: 'round' })
    ),
    heart: React.createElement('svg', { width: size, height: size, viewBox: '0 0 20 20', fill: 'none' },
      React.createElement('path', { d: 'M10 17.5c-5-3.5-8-7-8-10.5a4.5 4.5 0 0 1 8-2.8 4.5 4.5 0 0 1 8 2.8c0 3.5-3 7-8 10.5z', fill: color })
    ),
    triangle: React.createElement('svg', { width: size, height: size, viewBox: '0 0 20 20', fill: 'none' },
      React.createElement('path', { d: 'M10 2 18 16 2 16z', fill: color })
    ),
    bolt: React.createElement('svg', { width: size, height: size, viewBox: '0 0 20 20', fill: 'none' },
      React.createElement('path', { d: 'M12 2L6 9h4l-2 9 6-7h-4l2-9z', fill: color })
    )
  };

  return iconSVGs[iconType] || iconSVGs.checkmark;
};

// Template Preview Component
const TemplatePreview = ({ template, isSelected }) => {
  const { width, height } = template;
  const aspectRatio = width / height;

  // Calculate preview dimensions (max 60px)
  let previewWidth, previewHeight;
  if (aspectRatio > 1) {
    previewWidth = 60;
    previewHeight = 60 / aspectRatio;
  } else {
    previewHeight = 60;
    previewWidth = 60 * aspectRatio;
  }

  return React.createElement('div', {
    style: {
      ...styles.templatePreview,
      width: `${previewWidth}px`,
      height: `${previewHeight}px`,
      ...(isSelected && styles.templatePreviewSelected)
    }
  },
    React.createElement('div', {
      style: {
        position: 'absolute',
        top: '50%',
        left: '50%',
        transform: 'translate(-50%, -50%)',
        fontSize: '0.6rem',
        color: '#64748b',
        fontWeight: '500'
      }
    }, `${width}×${height}`)
  );
};

const AccordionSection = ({ title, children, isOpen, onToggle }) => {
    return React.createElement('div', { style: styles.accordionSection },
        React.createElement('div', { 
            style: { ...styles.accordionHeader, ...(isOpen && styles.accordionHeaderOpen) }, 
            onClick: onToggle,
            role: 'button',
            'aria-expanded': isOpen
        },
            React.createElement('h3', { style: styles.accordionTitle }, title),
            React.createElement(ChevronIcon, { isOpen })
        ),
        isOpen && React.createElement('div', { style: styles.accordionContent }, children)
    );
};

const EditPanel = ({ editableContent, onContentChange, onUpdateImage, selectedBulletIcon, setSelectedBulletIcon, bulletIconColor, setBulletIconColor }) => {
    const [newTag, setNewTag] = useState('');

    const handleAddTag = (e) => {
        if (e.key === 'Enter' && newTag.trim() !== '') {
            e.preventDefault();
            const trimmedTag = newTag.trim();
            if (!editableContent.tags.includes(trimmedTag)) {
                onContentChange('tags', [...editableContent.tags, trimmedTag]);
            }
            setNewTag('');
        }
    };

    const handleRemoveTag = (tagToRemove) => {
        onContentChange('tags', editableContent.tags.filter(tag => tag !== tagToRemove));
    };

    const handleFeatureChange = (index, value) => {
        const newFeatures = [...editableContent.free_plan_details];
        newFeatures[index] = value;
        onContentChange('free_plan_details', newFeatures);
    };

    const handleRemoveFeature = (index) => {
        const newFeatures = [...editableContent.free_plan_details];
        newFeatures.splice(index, 1);
        onContentChange('free_plan_details', newFeatures);
    };

    const handleAddFeature = () => {
        const currentFeatures = editableContent.free_plan_details || [];
        onContentChange('free_plan_details', [...currentFeatures, '']);
    };

    return React.createElement('div', { style: styles.editSection },
      React.createElement('div', { style: styles.editForm },
        React.createElement('div', { style: styles.editField },
          React.createElement('label', { htmlFor: 'product_name', style: styles.editLabel }, 'Product Name'),
          React.createElement('input', {
            id: 'product_name',
            type: 'text',
            style: {...styles.input, ...styles.editInputProminent},
            value: editableContent.product_name || '',
            onChange: (e) => onContentChange('product_name', e.target.value)
          })
        ),
        React.createElement('div', { style: styles.editField },
          React.createElement('label', { htmlFor: 'description', style: styles.editLabel }, 'Description'),
          React.createElement('textarea', {
            id: 'description',
            style: {...styles.textarea, ...styles.editTextAreaProminent},
            rows: 4,
            value: editableContent.description || '',
            onChange: (e) => onContentChange('description', e.target.value)
          })
        ),
        React.createElement('div', { style: styles.editField },
          React.createElement('label', { htmlFor: 'tags', style: styles.editLabel }, 'Tags'),
          React.createElement('div', { style: styles.tagsContainer},
            (editableContent.tags || []).map(tag => React.createElement('span', { key: tag, style: styles.tagPill },
                tag,
                React.createElement('button', { style: styles.tagRemoveBtn, onClick: () => handleRemoveTag(tag), 'aria-label': `Remove ${tag}` }, 
                    React.createElement(RemoveIcon, null)
                )
            )),
            React.createElement('input', {
                id: 'tags',
                type: 'text',
                style: { ...styles.input, border: 'none', flex: 1, minWidth: '120px', padding: '0.25rem' },
                placeholder: 'Add a tag...',
                value: newTag,
                onChange: e => setNewTag(e.target.value),
                onKeyDown: handleAddTag
            })
          )
        ),
        (editableContent.free_plan_details) &&
          React.createElement(React.Fragment, null,
            React.createElement('div', { style: styles.editField },
              React.createElement('label', { htmlFor: 'free_plan_title', style: styles.editLabel }, 'Free Plan Title'),
              React.createElement('input', {
                id: 'free_plan_title',
                type: 'text',
                style: styles.input,
                value: editableContent.free_plan_title || 'FREE PLAN',
                onChange: (e) => onContentChange('free_plan_title', e.target.value)
              })
            ),
            React.createElement('div', { style: styles.editField },
              React.createElement('label', { style: styles.editLabel }, 'Free Plan Features'),
              React.createElement('div', { style: { marginBottom: '1rem' } },
                React.createElement('label', { style: { ...styles.editLabel, fontSize: '0.9rem', marginBottom: '0.5rem', display: 'block' } }, 'Bullet Icon'),
                React.createElement('div', { style: styles.bulletIconGrid },
                  Object.keys({
                    checkmark: 'Checkmark',
                    circle: 'Circle',
                    square: 'Square',
                    diamond: 'Diamond',
                    star: 'Star',
                    arrow: 'Arrow',
                    plus: 'Plus',
                    heart: 'Heart',
                    triangle: 'Triangle',
                    bolt: 'Bolt'
                  }).map(iconType =>
                    React.createElement('div', {
                      key: iconType,
                      style: {
                        ...styles.bulletIconOption,
                        ...(selectedBulletIcon === iconType && styles.bulletIconOptionSelected)
                      },
                      onClick: () => setSelectedBulletIcon(iconType),
                      title: iconType.charAt(0).toUpperCase() + iconType.slice(1)
                    },
                      React.createElement(BulletIconSVG, { iconType, color: bulletIconColor, size: 20 })
                    )
                  )
                ),
                React.createElement('div', { style: { display: 'flex', alignItems: 'center', gap: '0.5rem', marginTop: '0.5rem' } },
                  React.createElement('label', { htmlFor: 'bullet-color', style: { ...styles.editLabel, fontSize: '0.9rem', margin: 0 } }, 'Color:'),
                  React.createElement('input', {
                    type: 'color',
                    id: 'bullet-color',
                    value: bulletIconColor,
                    onChange: (e) => setBulletIconColor(e.target.value),
                    style: { ...styles.colorInput, width: '40px', height: '30px' }
                  })
                )
              ),
              (editableContent.free_plan_details || []).map((feature, index) =>
                React.createElement('div', { key: index, style: styles.featureItem },
                    React.createElement('input', {
                        type: 'text',
                        style: styles.input,
                        value: feature,
                        onChange: e => handleFeatureChange(index, e.target.value),
                        'aria-label': `Feature ${index + 1}`
                    }),
                    React.createElement('button', { style: styles.featureRemoveBtn, onClick: () => handleRemoveFeature(index), 'aria-label': `Remove feature ${index + 1}`}, 'Remove')
                )
              ),
              React.createElement('button', { type: 'button', onClick: handleAddFeature, style: styles.addFeatureBtn}, 'Add Feature')
            )
          ),
        React.createElement('button', { onClick: onUpdateImage, style: {...styles.button, ...styles.updateImageBtn} }, 'Update Image')
      )
    )
  };
  
const Footer = () => React.createElement('footer', { style: styles.footer },
    React.createElement('p', null,
        'Powered by ',
        React.createElement('a', { href: 'https://www.jermesa.com', target: '_blank', rel: 'noopener noreferrer', style: styles.footerLink }, 'Jermesa Studio'),
        ' | ',
        React.createElement('a', { href: 'https://jermesa.com/privacy-policy/', target: '_blank', rel: 'noopener noreferrer', style: styles.footerLink }, 'Privacy Policy')
    ),
    React.createElement('p', { style: { fontSize: '0.8rem', marginTop: '0.5rem' } },
        "This site uses the 'Poppins' font, available under the SIL Open Font License."
    )
);

const CookieConsentBanner = ({ onAccept }) => {
    return React.createElement('div', { style: styles.cookieBanner },
        React.createElement('p', { style: styles.cookieText }, 'We use cookies to ensure you get the best experience. By using our site, you agree to our use of cookies.'),
        React.createElement('button', { onClick: onAccept, style: styles.cookieButton }, 'Got it!')
    );
};

const App = () => {
  const [apiKey, setApiKey] = useState('');
  const [apiKeyInput, setApiKeyInput] = useState('');
  const [isKeyValid, setIsKeyValid] = useState(false);

  const [url, setUrl] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [loadingMessage, setLoadingMessage] = useState('');
  const [generatedPost, setGeneratedPost] = useState(null);
  const [error, setError] = useState(null);
  const [backgroundColor, setBackgroundColor] = useState('#f8fafc');
  const [accentColor, setAccentColor] = useState('#4f46e5');
  const [showPriceSticker, setShowPriceSticker] = useState(true);
  const [selectedBulletIcon, setSelectedBulletIcon] = useState('checkmark');
  const [bulletIconColor, setBulletIconColor] = useState('#334155');

  // Template size definitions
  const templateSizes = {
    'square': { width: 1080, height: 1080, name: 'Square (1:1)', description: '1080×1080px - Instagram Post' },
    'portrait': { width: 1080, height: 1350, name: 'Portrait (4:5)', description: '1080×1350px - Instagram Portrait' },
    'story': { width: 1080, height: 1920, name: 'Story (9:16)', description: '1080×1920px - Instagram/Facebook Story' },
    'landscape': { width: 1200, height: 630, name: 'Landscape (1.91:1)', description: '1200×630px - Facebook/LinkedIn Post' },
    'twitter': { width: 1200, height: 675, name: 'Twitter (16:9)', description: '1200×675px - Twitter/X Post' },
    'pinterest': { width: 1000, height: 1500, name: 'Pinterest (2:3)', description: '1000×1500px - Pinterest Pin' }
  };

  const [selectedTemplate, setSelectedTemplate] = useState('square');

  // State for manual screenshot upload and cropping
  const [uploadedImage, setUploadedImage] = useState(null);
  const [isCropping, setIsCropping] = useState(false);
  const [cropTransform, setCropTransform] = useState({ x: 0, y: 0, scale: 1 });
  const [croppedScreenshot, setCroppedScreenshot] = useState(null);
  const cropCanvasRef = useRef(null);
  const isDragging = useRef(false);
  const lastMousePos = useRef({ x: 0, y: 0 });

  // State for content and caption generation
  const [structuredContent, setStructuredContent] = useState(null);
  const [editableContent, setEditableContent] = useState(null);
  const [isCaptionLoading, setIsCaptionLoading] = useState(false);
  const [generatedCaption, setGeneratedCaption] = useState('');
  const [copyButtonText, setCopyButtonText] = useState('Copy');

  // New state for share modal and copy image button
  const [isShareModalOpen, setIsShareModalOpen] = useState(false);
  const [shareModalContent, setShareModalContent] = useState(null);
  const [copyImageButtonText, setCopyImageButtonText] = useState('Copy Image');
  
  // State for accordion sections
  const [openSections, setOpenSections] = useState({
    inputs: true,
    style: true,
    edit: true,
    caption: true,
    export: true,
  });

  // State for cookie banner
  const [showCookieBanner, setShowCookieBanner] = useState(false);

  const toggleSection = (section) => {
    setOpenSections(prev => ({ ...prev, [section]: !prev[section] }));
  };

  useEffect(() => {
    const savedKey = localStorage.getItem('gemini_api_key');
    if (savedKey) {
        setApiKey(savedKey);
        setIsKeyValid(true);
    }
    const consent = localStorage.getItem('cookie_consent');
    if (!consent) {
        setShowCookieBanner(true);
    }
  }, []);

  useEffect(() => {
    const handleResize = () => {
      const appContainer = document.querySelector('#root > div');
      const layout = appContainer.querySelector(':first-child');
      if (window.innerWidth <= 900) {
        if (appContainer) appContainer.style.padding = '1rem';
        if (layout) layout.style.gridTemplateColumns = '1fr';
      } else {
        if (appContainer) appContainer.style.padding = '2rem';
        if (layout) layout.style.gridTemplateColumns = '1.5fr 1fr';
      }
    };
    window.addEventListener('resize', handleResize);
    handleResize(); // Initial check
    return () => window.removeEventListener('resize', handleResize);
  }, []);
  
  const handleAcceptCookies = () => {
    localStorage.setItem('cookie_consent', 'true');
    setShowCookieBanner(false);
  };

  const handleSaveKey = () => {
      if (apiKeyInput.trim()) {
          localStorage.setItem('gemini_api_key', apiKeyInput);
          setApiKey(apiKeyInput);
          setIsKeyValid(true);
          setApiKeyInput('');
      }
  };

  const handleClearKey = () => {
      localStorage.removeItem('gemini_api_key');
      setApiKey('');
      setIsKeyValid(false);
  };

  const wrapText = (context, text, x, y, maxWidth, lineHeight, maxLines = Infinity) => {
    const words = (text || '').split(' ');
    const lines = [];
    let currentLine = '';

    for (let i = 0; i < words.length; i++) {
        const word = words[i];
        const testLine = currentLine.length > 0 ? currentLine + ' ' + word : word;
        const metrics = context.measureText(testLine);
        if (metrics.width > maxWidth && currentLine.length > 0) {
            lines.push(currentLine);
            currentLine = word;
        } else {
            currentLine = testLine;
        }
    }
    lines.push(currentLine);

    let truncated = false;
    if (lines.length > maxLines) {
        lines.splice(maxLines);
        truncated = true;
    }
    
    if (truncated) {
        let lastLine = lines[maxLines - 1];
        while (context.measureText(lastLine + '...').width > maxWidth && lastLine.length > 0) {
            lastLine = lastLine.slice(0, -1);
        }
        lines[maxLines - 1] = lastLine.trimEnd() + '...';
    }

    let currentY = y;
    for (const line of lines) {
        context.fillText(line, x, currentY);
        currentY += lineHeight;
    }
    
    return currentY;
  };

  const drawRoundedRect = (ctx, x, y, width, height, radius) => {
    ctx.beginPath();
    ctx.moveTo(x + radius, y);
    ctx.lineTo(x + width - radius, y);
    ctx.quadraticCurveTo(x + width, y, x + width, y + radius);
    ctx.lineTo(x + width, y + height - radius);
    ctx.quadraticCurveTo(x + width, y + height, x + width - radius, y + height);
    ctx.lineTo(x + radius, y + height);
    ctx.quadraticCurveTo(x, y + height, x, y + height - radius);
    ctx.lineTo(x, y + radius);
    ctx.quadraticCurveTo(x, y, x + radius, y);
    ctx.closePath();
  };
    
  const drawDashedRoundedRect = (ctx, x, y, width, height, radius) => {
    ctx.save();
    ctx.beginPath();
    ctx.moveTo(x + radius, y);
    ctx.lineTo(x + width - radius, y);
    ctx.quadraticCurveTo(x + width, y, x + width, y + radius);
    ctx.lineTo(x + width, y + height - radius);
    ctx.quadraticCurveTo(x + width, y + height, x + width - radius, y + height);
    ctx.lineTo(x + radius, y + height);
    ctx.quadraticCurveTo(x, y + height, x, y + height - radius);
    ctx.lineTo(x, y + radius);
    ctx.quadraticCurveTo(x, y, x + radius, y);
    ctx.closePath();
    ctx.setLineDash([15, 10]);
    ctx.strokeStyle = '#cbd5e1';
    ctx.lineWidth = 3;
    ctx.stroke();
    ctx.restore();
  };

  const drawImageWithAspectRatio = (ctx, img, x, y, maxWidth, maxHeight) => {
    const ratio = Math.min(maxWidth / img.width, maxHeight / img.height);
    const width = img.width * ratio;
    const height = img.height * ratio;
    const newX = x + (maxWidth - width) / 2; // Center horizontally
    const newY = y + (maxHeight - height) / 2; // Center vertically
    ctx.drawImage(img, newX, newY, width, height);
  };
  
  // Bullet icon drawing functions
  const bulletIcons = {
    checkmark: (ctx, x, y, size, color) => {
      ctx.save();
      ctx.strokeStyle = color;
      ctx.lineWidth = size / 7;
      ctx.lineCap = 'round';
      ctx.lineJoin = 'round';
      ctx.beginPath();
      ctx.moveTo(x + size * 0.2, y + size * 0.5);
      ctx.lineTo(x + size * 0.45, y + size * 0.75);
      ctx.lineTo(x + size * 0.8, y + size * 0.25);
      ctx.stroke();
      ctx.restore();
    },
    circle: (ctx, x, y, size, color) => {
      ctx.save();
      ctx.fillStyle = color;
      ctx.beginPath();
      ctx.arc(x + size * 0.5, y + size * 0.5, size * 0.3, 0, Math.PI * 2);
      ctx.fill();
      ctx.restore();
    },
    square: (ctx, x, y, size, color) => {
      ctx.save();
      ctx.fillStyle = color;
      ctx.fillRect(x + size * 0.2, y + size * 0.2, size * 0.6, size * 0.6);
      ctx.restore();
    },
    diamond: (ctx, x, y, size, color) => {
      ctx.save();
      ctx.fillStyle = color;
      ctx.beginPath();
      ctx.moveTo(x + size * 0.5, y + size * 0.1);
      ctx.lineTo(x + size * 0.9, y + size * 0.5);
      ctx.lineTo(x + size * 0.5, y + size * 0.9);
      ctx.lineTo(x + size * 0.1, y + size * 0.5);
      ctx.closePath();
      ctx.fill();
      ctx.restore();
    },
    star: (ctx, x, y, size, color) => {
      ctx.save();
      ctx.fillStyle = color;
      ctx.beginPath();
      const centerX = x + size * 0.5;
      const centerY = y + size * 0.5;
      const outerRadius = size * 0.4;
      const innerRadius = size * 0.2;
      for (let i = 0; i < 10; i++) {
        const angle = (i * Math.PI) / 5;
        const radius = i % 2 === 0 ? outerRadius : innerRadius;
        const pointX = centerX + Math.cos(angle - Math.PI / 2) * radius;
        const pointY = centerY + Math.sin(angle - Math.PI / 2) * radius;
        if (i === 0) ctx.moveTo(pointX, pointY);
        else ctx.lineTo(pointX, pointY);
      }
      ctx.closePath();
      ctx.fill();
      ctx.restore();
    },
    arrow: (ctx, x, y, size, color) => {
      ctx.save();
      ctx.fillStyle = color;
      ctx.beginPath();
      ctx.moveTo(x + size * 0.1, y + size * 0.4);
      ctx.lineTo(x + size * 0.6, y + size * 0.4);
      ctx.lineTo(x + size * 0.6, y + size * 0.2);
      ctx.lineTo(x + size * 0.9, y + size * 0.5);
      ctx.lineTo(x + size * 0.6, y + size * 0.8);
      ctx.lineTo(x + size * 0.6, y + size * 0.6);
      ctx.lineTo(x + size * 0.1, y + size * 0.6);
      ctx.closePath();
      ctx.fill();
      ctx.restore();
    },
    plus: (ctx, x, y, size, color) => {
      ctx.save();
      ctx.strokeStyle = color;
      ctx.lineWidth = size / 6;
      ctx.lineCap = 'round';
      ctx.beginPath();
      ctx.moveTo(x + size * 0.5, y + size * 0.2);
      ctx.lineTo(x + size * 0.5, y + size * 0.8);
      ctx.moveTo(x + size * 0.2, y + size * 0.5);
      ctx.lineTo(x + size * 0.8, y + size * 0.5);
      ctx.stroke();
      ctx.restore();
    },
    heart: (ctx, x, y, size, color) => {
      ctx.save();
      ctx.fillStyle = color;
      ctx.beginPath();
      const centerX = x + size * 0.5;
      const centerY = y + size * 0.3;
      const width = size * 0.6;
      const height = size * 0.6;
      ctx.moveTo(centerX, centerY + height / 4);
      ctx.bezierCurveTo(centerX, centerY, centerX - width / 2, centerY, centerX - width / 2, centerY + height / 4);
      ctx.bezierCurveTo(centerX - width / 2, centerY + height / 2, centerX, centerY + height * 0.75, centerX, centerY + height);
      ctx.bezierCurveTo(centerX, centerY + height * 0.75, centerX + width / 2, centerY + height / 2, centerX + width / 2, centerY + height / 4);
      ctx.bezierCurveTo(centerX + width / 2, centerY, centerX, centerY, centerX, centerY + height / 4);
      ctx.fill();
      ctx.restore();
    },
    triangle: (ctx, x, y, size, color) => {
      ctx.save();
      ctx.fillStyle = color;
      ctx.beginPath();
      ctx.moveTo(x + size * 0.5, y + size * 0.1);
      ctx.lineTo(x + size * 0.9, y + size * 0.8);
      ctx.lineTo(x + size * 0.1, y + size * 0.8);
      ctx.closePath();
      ctx.fill();
      ctx.restore();
    },
    bolt: (ctx, x, y, size, color) => {
      ctx.save();
      ctx.fillStyle = color;
      ctx.beginPath();
      ctx.moveTo(x + size * 0.6, y + size * 0.1);
      ctx.lineTo(x + size * 0.3, y + size * 0.45);
      ctx.lineTo(x + size * 0.5, y + size * 0.45);
      ctx.lineTo(x + size * 0.4, y + size * 0.9);
      ctx.lineTo(x + size * 0.7, y + size * 0.55);
      ctx.lineTo(x + size * 0.5, y + size * 0.55);
      ctx.closePath();
      ctx.fill();
      ctx.restore();
    }
  };

  const drawBulletIcon = (ctx, x, y, size, iconType, color) => {
    if (bulletIcons[iconType]) {
      bulletIcons[iconType](ctx, x, y, size, color);
    } else {
      bulletIcons.checkmark(ctx, x, y, size, color);
    }
  };

  const drawCanvas = useCallback(async (content, screenshot) => {
      if (!content || !screenshot) return;

      setLoadingMessage('Preparing assets...');
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d');

      // Get current template dimensions
      const template = templateSizes[selectedTemplate];
      canvas.width = template.width;
      canvas.height = template.height;

      // Calculate scaling factors based on original 1080x1080 design
      const scaleX = template.width / 1080;
      const scaleY = template.height / 1080;
      const scale = Math.min(scaleX, scaleY); // Use minimum to maintain proportions

      const screenshotImage = new Image();
      screenshotImage.crossOrigin = 'anonymous';
      screenshotImage.src = screenshot;

      const logoImage = new Image();
      logoImage.crossOrigin = 'anonymous';
      let logoLoaded = false;
      if(content.logo_url){
          logoImage.src = content.logo_url;
      }
      
      const loadImage = (image) => new Promise((resolve, reject) => {
        if (image.complete) {
            resolve(image);
        } else {
            image.onload = () => resolve(image);
            image.onerror = reject;
        }
      });

      await loadImage(screenshotImage);
      try {
        if(content.logo_url) {
            await loadImage(logoImage);
            logoLoaded = true;
        }
      } catch (e) {
        console.warn("Could not load company logo:", e);
        logoLoaded = false;
      }

      setLoadingMessage('Ensuring font compliance...');
      await document.fonts.load('400 10px Poppins');
      await document.fonts.load('600 10px Poppins');
      await document.fonts.load('700 10px Poppins');

      setLoadingMessage('Assembling your post...');

      ctx.fillStyle = backgroundColor;
      ctx.fillRect(0, 0, canvas.width, canvas.height);

      // Scale all coordinates and sizes
      const logoX = 80 * scaleX;
      const logoY = 80 * scaleY;
      const logoMaxWidth = 300 * scaleX;
      const logoMaxHeight = 100 * scaleY;

      if(logoLoaded){
          drawImageWithAspectRatio(ctx, logoImage, logoX, logoY, logoMaxWidth, logoMaxHeight);
      } else {
        ctx.font = `bold ${48 * scale}px Poppins`;
        ctx.fillStyle = '#1e293b';
        ctx.fillText(content.product_name, logoX, 140 * scaleY);
      }

      // Screenshot area - adjust for different aspect ratios
      const screenshotX = 80 * scaleX;
      const screenshotY = 220 * scaleY;
      const screenshotWidth = 480 * scaleX;
      const screenshotHeight = Math.min(780 * scaleY, canvas.height - screenshotY - (80 * scaleY));

      ctx.save();
      ctx.shadowColor = 'rgba(0, 0, 0, 0.1)';
      ctx.shadowBlur = 30 * scale;
      ctx.shadowOffsetY = 10 * scale;
      drawRoundedRect(ctx, screenshotX, screenshotY, screenshotWidth, screenshotHeight, 20 * scale);
      ctx.clip();
      ctx.drawImage(screenshotImage, screenshotX, screenshotY, screenshotWidth, screenshotHeight);
      ctx.restore();

      const rightColX = 620 * scaleX;

      // Right column info box - adjust size for different templates
      const rightColWidth = Math.min(380 * scaleX, canvas.width - rightColX - (80 * scaleX));
      const rightColHeight = Math.min(420 * scaleY, canvas.height - (160 * scaleY));

      ctx.fillStyle = 'white';
      ctx.shadowColor = 'rgba(0, 0, 0, 0.08)';
      ctx.shadowBlur = 30 * scale;
      ctx.shadowOffsetY = 5 * scale;
      drawRoundedRect(ctx, rightColX, 80 * scaleY, rightColWidth, rightColHeight, 20 * scale);
      ctx.fill();
      ctx.shadowColor = 'transparent';

      ctx.fillStyle = '#1e293b';
      ctx.font = `bold ${42 * scale}px Poppins`;
      ctx.textAlign = 'left';
      let yPos = wrapText(ctx, content.product_name, rightColX + (30 * scaleX), 140 * scaleY, (rightColWidth - 60 * scaleX), 50 * scale);

      yPos += 10 * scale;
      ctx.font = `bold ${18 * scale}px Poppins`;
      let xPos = rightColX + (30 * scaleX);
      (content.tags || []).slice(0, 3).forEach(tag => {
          const tagWidth = ctx.measureText(tag).width + (24 * scaleX);
          if (xPos + tagWidth > rightColX + (rightColWidth - 30 * scaleX)) {
              xPos = rightColX + (30 * scaleX);
              yPos += 40 * scale;
          }
          ctx.fillStyle = accentColor;
          drawRoundedRect(ctx, xPos, yPos, tagWidth, 32 * scale, 16 * scale);
          ctx.fill();
          ctx.fillStyle = 'white';
          ctx.fillText(tag, xPos + (12 * scaleX), yPos + (22 * scale));
          xPos += tagWidth + (10 * scaleX);
      });
      yPos += 60 * scale;

      ctx.fillStyle = '#475569';
      ctx.font = `${22 * scale}px Poppins`;
      wrapText(ctx, content.description, rightColX + (30 * scaleX), yPos, rightColWidth - (60 * scaleX), 30 * scale, 5);

      if (content.free_plan_details && content.free_plan_details.length > 0) {
        const freePlanCardY = Math.max(540 * scaleY, yPos + (40 * scale));
        const freePlanCardHeight = Math.min(460 * scaleY, canvas.height - freePlanCardY - (80 * scaleY));
        drawDashedRoundedRect(ctx, rightColX, freePlanCardY, rightColWidth, freePlanCardHeight, 20 * scale);

        ctx.fillStyle = '#1e293b';
        ctx.font = `bold ${32 * scale}px Poppins`;
        const freePlanText = content.free_plan_title || 'FREE PLAN';
        const freePlanTextMetrics = ctx.measureText(freePlanText);
        const freePlanTextX = rightColX + (30 * scaleX);
        const freePlanTextY = freePlanCardY + (60 * scaleY);
        ctx.fillText(freePlanText, freePlanTextX, freePlanTextY);

        if (showPriceSticker) {
          const stickerRadius = 28 * scale;
          const stickerX = freePlanTextX + freePlanTextMetrics.width + stickerRadius + (15 * scaleX);
          const stickerY = freePlanTextY - (10 * scaleY);
          ctx.save();
          ctx.shadowColor = 'rgba(0, 0, 0, 0.2)';
          ctx.shadowBlur = 8 * scale;
          ctx.shadowOffsetY = 4 * scale;
          ctx.fillStyle = '#f97316';
          ctx.beginPath();
          ctx.arc(stickerX, stickerY, stickerRadius, 0, Math.PI * 2);
          ctx.fill();
          ctx.restore();
          ctx.fillStyle = 'white';
          ctx.font = `bold ${28 * scale}px Poppins`;
          ctx.textAlign = 'center';
          ctx.textBaseline = 'middle';
          ctx.fillText('$0', stickerX, stickerY + (2 * scale));
          ctx.textAlign = 'left';
          ctx.textBaseline = 'alphabetic';
        }

        yPos = freePlanCardY + (110 * scaleY);
        ctx.font = `${22 * scale}px Poppins`;
        ctx.fillStyle = '#334155';
        content.free_plan_details.slice(0, 8).forEach(detail => {
            const iconSize = 18 * scale;
            const textX = rightColX + (30 * scaleX) + iconSize + (10 * scaleX);
            const textWidth = rightColWidth - (60 * scaleX) - iconSize - (10 * scaleX);
            drawBulletIcon(ctx, rightColX + (30 * scaleX), yPos - iconSize, iconSize, selectedBulletIcon, bulletIconColor);
            yPos = wrapText(ctx, detail, textX, yPos, textWidth, 32 * scale, 2);
            yPos += 12 * scale;
        });
      }

      try {
        const sourceHostname = new URL(url).hostname.replace('www.', '');
        ctx.fillStyle = '#94a3b8';
        ctx.font = `${20 * scale}px Poppins`;
        ctx.textAlign = 'center';
        ctx.fillText(`Source: ${sourceHostname}`, canvas.width / 2, canvas.height - (30 * scaleY));
      } catch (e) {
        console.warn("Could not parse URL for source attribution:", e);
      }

      setGeneratedPost(canvas.toDataURL('image/png'));
  }, [backgroundColor, accentColor, showPriceSticker, url, selectedBulletIcon, bulletIconColor, selectedTemplate]);

  const fetchAndProcessContent = async (e) => {
    e.preventDefault();
    if (!url) {
      setError('Please enter a URL.');
      return;
    }
    if (!croppedScreenshot) {
      setError('Please upload and crop a screenshot before generating.');
      return;
    }

    setIsLoading(true);
    setError(null);
    setGeneratedPost(null);
    setGeneratedCaption('');
    setStructuredContent(null);
    setEditableContent(null);

    const ai = new GoogleGenAI({ apiKey });

    try {
      setLoadingMessage('Analyzing webpage...');
      const analysisResponse = await ai.models.generateContent({
        model: 'gemini-2.5-flash',
        contents: `Based on the content of the webpage at ${url}, provide the following: the company/product name, a short description, 2-4 relevant tags, the direct URL for the company logo, and the key features of any explicitly free pricing tier.`,
        config: { tools: [{ googleSearch: {} }] },
      });
      const analysisText = analysisResponse.text;
        
      const contentSchema = {
          type: Type.OBJECT,
          properties: {
              logo_url: { type: Type.STRING, description: "The absolute URL of the company's logo. If not found, return an empty string." },
              product_name: { type: Type.STRING, description: "The main name of the product or company." },
              tags: { type: Type.ARRAY, description: "An array of 2-4 short keywords.", items: { type: Type.STRING } },
              description: { type: Type.STRING, description: "A concise description." },
              free_plan_details: { type: Type.ARRAY, description: "An array of the top 4-6 most important features from a plan that is explicitly and genuinely free (e.g., costs $0). If no such plan exists, this MUST be an empty array.", items: { type: Type.STRING } },
          },
          required: ['logo_url', 'product_name', 'tags', 'description', 'free_plan_details'],
      };

      const structuredContentResponse = await ai.models.generateContent({
        model: 'gemini-2.5-flash',
        contents: `From the following text, extract the specified information: ${analysisText}`,
        config: { responseMimeType: 'application/json', responseSchema: contentSchema },
      });
      const parsedContent = JSON.parse(structuredContentResponse.text);
      
      const initialEditableState = {
        ...parsedContent,
        free_plan_title: 'FREE PLAN'
      };

      setStructuredContent(parsedContent);
      setEditableContent(initialEditableState);
      drawCanvas(initialEditableState, croppedScreenshot);

    } catch (err) {
      console.error(err);
      if (err.message && err.message.includes('API key not valid')) {
        setError('Your API Key is invalid. Please enter a valid key to continue.');
        handleClearKey();
      } else {
        setError('An error occurred while generating the post. Please try again.');
      }
    } finally {
        setIsLoading(false);
    }
  };

  const handleContentChange = (field, value) => {
      setEditableContent(prev => ({ ...prev, [field]: value }));
  };

  const handleUpdateImage = () => {
    drawCanvas(editableContent, croppedScreenshot);
  };

  const generateCaption = async () => {
    if (!editableContent) return;
    setIsCaptionLoading(true);
    setGeneratedCaption('');
    setError(null);
    
    const ai = new GoogleGenAI({ apiKey });

    try {
      const prompt = `You are a social media marketing expert. Based on the following information, write an engaging and SEO-friendly social media post caption.
      
      Product Name: ${editableContent.product_name}
      Description: ${editableContent.description}
      Tags/Keywords: ${editableContent.tags.join(', ')}
      Source Link: ${url}
      
      Your caption should:
      1. Be enthusiastic and engaging.
      2. Clearly state what the product does and its main benefit.
      3. Include a call-to-action that directs users to the Source Link (e.g., "Learn more at ${url}!").
      4. End with 3-5 relevant and popular hashtags.
      
      Do not add any disclaimers or extra text outside of the main caption content.`;

      const response = await ai.models.generateContent({
        model: 'gemini-2.5-flash',
        contents: prompt,
      });

      const marketingCaption = response.text.trim();
      const disclaimer = `\n\n---\n\nDisclaimer: All brand properties, logos, and trademarks belong to their respective owners. Some of the information and imagery in this post were generated by AI and should be verified with the source. If you are the owner and would like this post removed, please contact us.`;

      setGeneratedCaption(marketingCaption + disclaimer);
    } catch (err) {
      console.error("Caption generation failed:", err);
      if (err.message && err.message.includes('API key not valid')) {
        setError('Your API Key is invalid. Please enter a valid key to continue.');
        handleClearKey();
      } else {
        setError("Could not generate a caption. Please try again.");
      }
    } finally {
      setIsCaptionLoading(false);
    }
  };

  const copyToClipboard = () => {
    navigator.clipboard.writeText(generatedCaption).then(() => {
      setCopyButtonText('Copied!');
      setTimeout(() => setCopyButtonText('Copy'), 2000);
    }, (err) => {
      console.error('Could not copy text: ', err);
    });
  };

  const handleCopyImage = async () => {
    if (!generatedPost) return;
    try {
      const response = await fetch(generatedPost);
      const blob = await response.blob();
      
      await navigator.clipboard.write([
        new ClipboardItem({
          [blob.type]: blob,
        }),
      ]);
      
      setCopyImageButtonText('Copied!');
      setTimeout(() => setCopyImageButtonText('Copy Image'), 2000);

    } catch (err) {
      console.error('Failed to copy image: ', err);
      alert('Sorry, your browser does not support copying images to the clipboard, or an error occurred. Please use the download button.');
    }
  };

  const downloadImage = () => {
    const link = document.createElement('a');
    link.href = generatedPost;
    link.download = 'social-media-post.png';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  const openShareModal = (platform) => {
    if (!generatedCaption) return;
    
    navigator.clipboard.writeText(generatedCaption).then(() => {
        let content;
        if (platform === 'twitter') {
            const tweetText = encodeURIComponent(generatedCaption.split('\n\n---\n\n')[0]); // Share only the caption, not disclaimer
            content = {
                title: 'Share to Twitter',
                instructions: [
                    'Your caption has been copied to the clipboard.',
                    'Click "Continue" to open Twitter with the caption pre-filled.',
                    'Remember to upload the image you downloaded or copied.'
                ],
                url: `https://twitter.com/intent/tweet?text=${tweetText}`,
                cta: 'Continue to Twitter'
            };
        } else { // instagram
            content = {
                title: 'Post to Instagram',
                instructions: [
                    'Your caption has been copied to the clipboard.',
                    'Make sure you have downloaded or copied the image.',
                    'Open the Instagram app or website to create your post.',
                    'Paste the caption and upload your image there.'
                ],
                url: 'https://www.instagram.com',
                cta: 'Open Instagram'
            };
        }
        setShareModalContent(content);
        setIsShareModalOpen(true);
    }).catch(err => {
        console.error('Failed to copy caption: ', err);
        alert("Could not copy the caption. Please copy it manually.");
    });
  };
  
  // --- CROPPER LOGIC ---
  // Calculate crop box dimensions based on selected template
  const getCropBoxDimensions = () => {
    const template = templateSizes[selectedTemplate];
    const screenshotRatio = 480 / 780; // Original screenshot area ratio
    const templateRatio = template.width / template.height;

    // Base dimensions for cropper (scaled to fit in modal)
    const maxCropWidth = 480;
    const maxCropHeight = 600;

    let cropWidth, cropHeight;
    if (templateRatio > screenshotRatio) {
      // Template is wider, use full width
      cropWidth = maxCropWidth;
      cropHeight = maxCropWidth / templateRatio;
    } else {
      // Template is taller, use full height
      cropHeight = Math.min(maxCropHeight, maxCropWidth / templateRatio);
      cropWidth = cropHeight * templateRatio;
    }

    return { width: cropWidth, height: cropHeight };
  };

  const cropBoxDimensions = getCropBoxDimensions();
  const CROP_BOX_WIDTH = cropBoxDimensions.width;
  const CROP_BOX_HEIGHT = cropBoxDimensions.height;
  
  const handleFileChange = (e) => {
    if (e.target.files && e.target.files[0]) {
      const file = e.target.files[0];
      const reader = new FileReader();
      reader.onload = (event) => {
        setUploadedImage(event.target.result);
        setIsCropping(true);
        setCropTransform({ x: 0, y: 0, scale: 1 });
      };
      reader.readAsDataURL(file);
    }
  };
  
  const handlePaste = useCallback((event) => {
    const items = event.clipboardData.items;
    for (let i = 0; i < items.length; i++) {
        if (items[i].type.indexOf('image') !== -1) {
            const file = items[i].getAsFile();
            const reader = new FileReader();
            reader.onload = (e) => {
                setUploadedImage(e.target.result);
                setIsCropping(true);
                setCropTransform({ x: 0, y: 0, scale: 1 });
            };
            reader.readAsDataURL(file);
            break;
        }
    }
  }, []);

  useEffect(() => {
    window.addEventListener('paste', handlePaste);
    return () => {
      window.removeEventListener('paste', handlePaste);
    };
  }, [handlePaste]);

  const drawCropper = useCallback(() => {
    if (!isCropping || !cropCanvasRef.current || !uploadedImage) return;

    const canvas = cropCanvasRef.current;
    const ctx = canvas.getContext('2d');
    const img = new Image();
    img.src = uploadedImage;
    img.onload = () => {
      ctx.fillStyle = '#f0f0f0';
      ctx.fillRect(0, 0, canvas.width, canvas.height);
      
      const scaledWidth = img.width * cropTransform.scale;
      const scaledHeight = img.height * cropTransform.scale;
      
      ctx.drawImage(img, cropTransform.x, cropTransform.y, scaledWidth, scaledHeight);
      
      // Draw overlay
      const overlayX = (canvas.width - CROP_BOX_WIDTH) / 2;
      const overlayY = (canvas.height - CROP_BOX_HEIGHT) / 2;
      
      ctx.fillStyle = 'rgba(0, 0, 0, 0.5)';
      ctx.fillRect(0, 0, canvas.width, overlayY);
      ctx.fillRect(0, overlayY + CROP_BOX_HEIGHT, canvas.width, canvas.height - (overlayY + CROP_BOX_HEIGHT));
      ctx.fillRect(0, overlayY, overlayX, CROP_BOX_HEIGHT);
      ctx.fillRect(overlayX + CROP_BOX_WIDTH, overlayY, canvas.width - (overlayX + CROP_BOX_WIDTH), CROP_BOX_HEIGHT);

      ctx.strokeStyle = 'white';
      ctx.setLineDash([10, 5]);
      ctx.strokeRect(overlayX, overlayY, CROP_BOX_WIDTH, CROP_BOX_HEIGHT);
    };
  }, [isCropping, uploadedImage, cropTransform, selectedTemplate]);

  useEffect(() => {
    drawCropper();
  }, [drawCropper]);

  const handleMouseDown = (e) => {
    isDragging.current = true;
    lastMousePos.current = { x: e.clientX, y: e.clientY };
  };

  const handleMouseMove = (e) => {
    if (isDragging.current) {
      const dx = e.clientX - lastMousePos.current.x;
      const dy = e.clientY - lastMousePos.current.y;
      setCropTransform(prev => ({ ...prev, x: prev.x + dx, y: prev.y + dy }));
      lastMousePos.current = { x: e.clientX, y: e.clientY };
    }
  };

  const handleMouseUp = () => {
    isDragging.current = false;
  };
  
  const handleWheel = (e) => {
    e.preventDefault();
    const scaleAmount = -e.deltaY * 0.001;
    setCropTransform(prev => ({
        ...prev,
        scale: Math.max(0.1, prev.scale + scaleAmount)
    }));
  };

  const handleCrop = () => {
    const img = new Image();
    img.src = uploadedImage;
    img.onload = () => {
        const finalCanvas = document.createElement('canvas');
        finalCanvas.width = CROP_BOX_WIDTH;
        finalCanvas.height = CROP_BOX_HEIGHT;
        const ctx = finalCanvas.getContext('2d');
        
        const sourceX = ((cropCanvasRef.current.width - CROP_BOX_WIDTH) / 2 - cropTransform.x) / cropTransform.scale;
        const sourceY = ((cropCanvasRef.current.height - CROP_BOX_HEIGHT) / 2 - cropTransform.y) / cropTransform.scale;
        const sourceWidth = CROP_BOX_WIDTH / cropTransform.scale;
        const sourceHeight = CROP_BOX_HEIGHT / cropTransform.scale;
        
        ctx.drawImage(img, sourceX, sourceY, sourceWidth, sourceHeight, 0, 0, CROP_BOX_WIDTH, CROP_BOX_HEIGHT);

        setCroppedScreenshot(finalCanvas.toDataURL('image/png'));
        setIsCropping(false);
        setUploadedImage(null);
    }
  };
  
  if (!isKeyValid) {
    return React.createElement('div', { style: styles.appContainer },
        React.createElement('div', { style: styles.apiKeyContainer },
            React.createElement('h2', { style: styles.apiKeyTitle }, 'Enter Your Gemini API Key'),
            React.createElement('p', { style: styles.apiKeySubtitle },
                "To use this app, please provide your API key from Google AI Studio. Your key is saved locally in your browser and never sent to any server but Google's."
            ),
            React.createElement('div', { style: styles.apiKeyForm },
                React.createElement('input', {
                    type: 'password',
                    value: apiKeyInput,
                    onChange: (e) => setApiKeyInput(e.target.value),
                    placeholder: 'Enter your API key...',
                    style: styles.input,
                    'aria-label': 'Gemini API Key'
                }),
                React.createElement('button', { onClick: handleSaveKey, style: styles.button }, 'Save & Continue')
            ),
            error && React.createElement('p', { style: styles.error }, error)
        ),
        React.createElement(Footer, null),
        showCookieBanner && React.createElement(CookieConsentBanner, { onAccept: handleAcceptCookies })
    );
  }

  return React.createElement('div', { style: styles.appContainer },
    // --- MODALS ---
    isCropping && React.createElement('div', { style: styles.modalOverlay },
      React.createElement('div', { style: styles.modalContent },
        React.createElement('h3', null, 'Crop Your Screenshot'),
        React.createElement('p', null, 'Pan (drag) and zoom (scroll) to position your image.'),
        React.createElement('canvas', {
          ref: cropCanvasRef,
          width: 800,
          height: 900,
          onMouseDown: handleMouseDown,
          onMouseMove: handleMouseMove,
          onMouseUp: handleMouseUp,
          onMouseLeave: handleMouseUp,
          onWheel: handleWheel,
          style: { 
            cursor: isDragging.current ? 'grabbing' : 'grab',
            maxWidth: '100%',
            maxHeight: 'calc(95vh - 200px)',
            height: 'auto'
          }
        }),
        React.createElement('div', { style: styles.modalActions },
          React.createElement('button', { onClick: handleCrop, style: {...styles.button, width: 'auto' } }, 'Confirm Crop'),
          React.createElement('button', { onClick: () => setIsCropping(false), style: styles.secondaryButton }, 'Cancel')
        )
      )
    ),
    isShareModalOpen && shareModalContent && React.createElement('div', { style: styles.shareModalOverlay },
      React.createElement('div', { style: styles.shareModalContent },
        React.createElement('h3', { style: styles.shareModalTitle }, shareModalContent.title),
        React.createElement('ul', { style: styles.shareModalInstructions },
          shareModalContent.instructions.map((line, index) => React.createElement('li', { key: index }, line))
        ),
        React.createElement('div', { style: styles.shareModalActions },
          React.createElement('a', { href: shareModalContent.url, target: '_blank', rel: 'noopener noreferrer', style: { ...styles.button, textDecoration: 'none', width: 'auto' }, onClick: () => setIsShareModalOpen(false) },
            shareModalContent.cta
          ),
          React.createElement('button', { onClick: () => setIsShareModalOpen(false), style: styles.secondaryButton }, 'Close')
        )
      )
    ),

    React.createElement('div', { style: styles.appLayout },
        // --- PREVIEW PANEL (LEFT) ---
        React.createElement('div', { style: styles.previewPanel },
            isLoading && React.createElement('div', { style: styles.loaderContainer },
                React.createElement('div', { style: styles.spinner }),
                React.createElement('p', { style: styles.loadingText }, loadingMessage)
            ),
            !isLoading && error && React.createElement('p', { style: styles.error }, error),
            !isLoading && !error && generatedPost && React.createElement('img', {
                src: generatedPost,
                alt: 'Generated social media post',
                style: styles.postImage
            }),
            !isLoading && !generatedPost && !error && React.createElement('div', { style: styles.placeholder },
                React.createElement('h2', { style: styles.placeholderTitle }, 'Your Post Will Appear Here'),
                React.createElement('p', null, 'Start by uploading a screenshot and entering a URL in the panel on the right.')
            )
        ),

        // --- CONTROLS SIDEBAR (RIGHT) ---
        React.createElement('div', { style: styles.controlsSidebar },
            React.createElement('header', { style: styles.header },
                React.createElement('div', { style: styles.headerContent },
                    React.createElement('h1', { style: styles.title }, 'Social Post Generator'),
                    React.createElement('p', { style: styles.subtitle }, 'Turn any webpage into a professional image.')
                ),
                React.createElement('button', { onClick: handleClearKey, style: styles.changeKeyButton }, 'Change API Key')
            ),

            React.createElement(AccordionSection, { title: '1. Inputs', isOpen: openSections.inputs, onToggle: () => toggleSection('inputs') },
                React.createElement('div', null,
                  React.createElement('label', { htmlFor: 'screenshot-upload', style: styles.uploadLabel },
                    React.createElement('span', null, 'Upload Screenshot (Required)'),
                    React.createElement('small', null, 'Click or paste an image from clipboard')
                  ),
                  React.createElement('input', { id: 'screenshot-upload', type: 'file', accept: 'image/*', onChange: handleFileChange, style: { display: 'none' } }),
                  croppedScreenshot && React.createElement('div', { style: styles.previewContainer },
                    React.createElement('img', { src: croppedScreenshot, alt: 'Cropped preview', style: styles.previewImage }),
                    React.createElement('button', { onClick: () => setCroppedScreenshot(null), style: styles.clearButton }, 'Clear')
                  )
                ),
                React.createElement('form', { onSubmit: fetchAndProcessContent, style: styles.form },
                  React.createElement('input', {
                    type: 'url',
                    value: url,
                    onChange: (e) => setUrl(e.target.value),
                    placeholder: 'Enter a webpage URL...',
                    style: styles.input,
                    'aria-label': 'Webpage URL',
                    required: true
                  }),
                  React.createElement('button', {
                    type: 'submit',
                    style: { ...styles.button, ...((isLoading || !croppedScreenshot) && styles.buttonDisabled) },
                    disabled: isLoading || !croppedScreenshot,
                    title: !croppedScreenshot ? 'Please upload and crop a screenshot first' : ''
                  }, isLoading ? 'Generating...' : 'Generate Post')
                )
            ),

            React.createElement(AccordionSection, { title: '2. Style & Options', isOpen: openSections.style, onToggle: () => toggleSection('style') },
                React.createElement('div', { style: styles.optionsContainer },
                  React.createElement('div', { style: { marginBottom: '1.5rem' } },
                    React.createElement('label', { style: { ...styles.colorLabel, display: 'block', marginBottom: '0.75rem' } }, 'Template Size'),
                    React.createElement('div', { style: styles.templateGrid },
                      Object.entries(templateSizes).map(([key, template]) =>
                        React.createElement('div', {
                          key: key,
                          style: {
                            ...styles.templateOption,
                            ...(selectedTemplate === key && styles.templateOptionSelected)
                          },
                          onClick: () => setSelectedTemplate(key)
                        },
                          React.createElement(TemplatePreview, { template, isSelected: selectedTemplate === key }),
                          React.createElement('div', { style: styles.templateName }, template.name),
                          React.createElement('div', { style: styles.templateDescription }, template.description)
                        )
                      )
                    )
                  ),
                  React.createElement('div', { style: styles.optionItem },
                    React.createElement('label', { htmlFor: 'background-color', style: styles.colorLabel }, 'Background'),
                    React.createElement('input', {
                      type: 'color',
                      id: 'background-color',
                      value: backgroundColor,
                      onChange: (e) => setBackgroundColor(e.target.value),
                      style: styles.colorInput
                    })
                  ),
                  React.createElement('div', { style: styles.optionItem },
                    React.createElement('label', { htmlFor: 'accent-color', style: styles.colorLabel }, 'Accent'),
                    React.createElement('input', {
                      type: 'color',
                      id: 'accent-color',
                      value: accentColor,
                      onChange: (e) => setAccentColor(e.target.value),
                      style: styles.colorInput
                    })
                  ),
                  React.createElement('div', { style: styles.optionItem },
                    React.createElement('label', { htmlFor: 'sticker-toggle', style: styles.colorLabel }, "Show '$0'"),
                    React.createElement('button', {
                      id: 'sticker-toggle',
                      role: 'switch',
                      'aria-checked': showPriceSticker,
                      onClick: () => setShowPriceSticker(!showPriceSticker),
                      style: { ...styles.switchTrack, backgroundColor: showPriceSticker ? 'var(--brand-color)' : '#e2e8f0' }
                    },
                      React.createElement('span', { style: { ...styles.switchKnob, transform: showPriceSticker ? 'translateX(22px)' : 'translateX(0px)' } })
                    )
                  )
                )
            ),
            
            generatedPost && editableContent && React.createElement(AccordionSection, { title: '3. Edit Post Content', isOpen: openSections.edit, onToggle: () => toggleSection('edit') },
                React.createElement(EditPanel, {
                    editableContent: editableContent,
                    onContentChange: handleContentChange,
                    onUpdateImage: handleUpdateImage,
                    selectedBulletIcon: selectedBulletIcon,
                    setSelectedBulletIcon: setSelectedBulletIcon,
                    bulletIconColor: bulletIconColor,
                    setBulletIconColor: setBulletIconColor
                })
            ),

            generatedPost && React.createElement(AccordionSection, { title: '4. Social Caption', isOpen: openSections.caption, onToggle: () => toggleSection('caption') },
                !generatedCaption && !isCaptionLoading && React.createElement('button', { onClick: generateCaption, style: styles.button }, 'Generate Caption'),
                isCaptionLoading && React.createElement('div', { style: styles.loaderContainer },
                    React.createElement('div', { style: styles.spinner }),
                    React.createElement('p', { style: styles.loadingText }, 'Generating caption...')
                ),
                generatedCaption && React.createElement('div', { style: styles.captionResult },
                    React.createElement('textarea', {
                        value: generatedCaption,
                        readOnly: true,
                        style: styles.captionTextarea,
                        rows: 8,
                        'aria-label': 'Generated Caption'
                    }),
                    React.createElement('button', { onClick: copyToClipboard, style: styles.copyButton }, copyButtonText)
                )
            ),

            generatedPost && React.createElement(AccordionSection, { title: '5. Export', isOpen: openSections.export, onToggle: () => toggleSection('export') },
                React.createElement('div', { style: styles.postActions },
                    React.createElement('button', { onClick: handleCopyImage, style: styles.downloadButton }, copyImageButtonText),
                    React.createElement('button', { onClick: downloadImage, style: styles.downloadButton }, 'Download Image'),
                    React.createElement('button', { onClick: () => openShareModal('twitter'), style: { ...styles.secondaryButton, ...styles.shareButton }, disabled: !generatedCaption }, 'Share to Twitter'),
                    React.createElement('button', { onClick: () => openShareModal('instagram'), style: { ...styles.secondaryButton, ...styles.shareButton }, disabled: !generatedCaption }, 'Share to Instagram')
                )
            )
        )
    ),
    React.createElement(Footer, null),
    showCookieBanner && React.createElement(CookieConsentBanner, { onAccept: handleAcceptCookies })
  );
};

const styleSheet = document.createElement("style");
styleSheet.type = "text/css";
styleSheet.innerText = `@keyframes spin { to { transform: rotate(360deg); } }`;
document.head.appendChild(styleSheet);


const root = ReactDOM.createRoot(document.getElementById('root'));
root.render(React.createElement(App));
    </script>
  <script type="module" src="/index.tsx"></script>
</body>
</html>